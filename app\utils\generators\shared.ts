export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export function randomCoeff(): number {
  const coeff = randomInt(-5, 5)
  return coeff === 0 ? 1 : coeff
}

export function randomNonZeroInt(min: number, max: number): number {
  let value = randomInt(min, max)
  while (value === 0) value = randomInt(min, max)
  return value
}


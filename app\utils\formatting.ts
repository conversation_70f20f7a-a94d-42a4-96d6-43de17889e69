// Shared formatting helpers for equations and terms

export function formatCoeff(coeff: number): string {
  if (coeff === 1) return ''
  if (coeff === -1) return '-'
  return coeff.toString()
}

export function formatTerm(coeff: number, variable: string): string {
  if (coeff === 0) return ''
  if (coeff > 0) return ` + ${coeff === 1 ? '' : coeff}${variable}`
  return ` - ${coeff === -1 ? '' : Math.abs(coeff)}${variable}`
}

export function formatConstant(constant: number, forceSign = false): string {
  if (constant === 0 && !forceSign) return ''
  if (constant === 0 && forceSign) return ' + 0'
  if (constant > 0) return ` + ${constant}`
  return ` - ${Math.abs(constant)}`
}

export function formatNonZeroConstant(constant: number): string {
  if (constant > 0) return ` + ${constant}`
  return ` - ${Math.abs(constant)}`
}

export function formatNonZeroTerm(coeff: number, variable: string): string {
  if (coeff > 0) return ` + ${coeff === 1 ? '' : coeff}${variable}`
  return ` - ${coeff === -1 ? '' : Math.abs(coeff)}${variable}`
}

// Replace inline x^2/x^3 shorthands used in display strings
export function simpleMathMarkup(expression: string): string {
  if (!expression) return ''
  return expression
    .replace(/x²/g, 'x<sup>2</sup>')
    .replace(/x³/g, 'x<sup>3</sup>')
    .replace(/([+-])/g, ' $1 ')
    .replace(/\s+/g, ' ')
    .trim()
}


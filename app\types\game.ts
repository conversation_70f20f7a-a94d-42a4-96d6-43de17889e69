// Game type definitions for the educational math application

export type GameMode = 'notation-conversion' | 'domain-range' | 'piecewise-functions'

export type NotationType = 'algebraic' | 'interval' | 'set-builder'

export type FunctionType =
  | 'linear'
  | 'quadratic'
  | 'cubic'
  | 'absolute'
  | 'square-root'
  | 'constant'

export type FunctionLetter = 'f' | 'g' | 'h' | 'p' | 'q'

export interface NotationAnswer {
  english: string
  setBuilder: string
  interval: string
}

export interface DisplayNotationProblem {
  id: string
  type: 'english' | 'set-builder' | 'interval'
  content: string
  description: string // e.g., "English Statement", "Set-Builder Notation"
  answer: NotationAnswer // All three notation formats for the same mathematical concept
}

export interface DomainRangeAnswer {
  domain: string
  range: string
  domainSetBuilder: string
  rangeSetBuilder: string
}

export interface DisplayDomainRangeProblem {
  id: string
  functionType: FunctionType
  equation: string
  questionType: 'domain' | 'range'
  description: string // e.g., "Find the Domain", "Find the Range"
  answer: DomainRangeAnswer // Both domain and range for the function
}

// Piecewise function types
export interface PiecewiseParams {
  // Common params
  a?: number
  b?: number
  c?: number
  h?: number
}

export interface PiecewisePiece {
  functionType: FunctionType
  equation: string
  condition: string // e.g., "-2 ≤ x < 1"
  leftBound: number
  rightBound: number
  leftInclusive: boolean
  rightInclusive: boolean
  // Typed parameters to allow evaluation without parsing strings
  params?: PiecewiseParams
}

export interface PiecewiseAnswer {
  applicablePiece: PiecewisePiece
  substitutionStep: string
  finalResult: string
  explanation: string
}

export interface DisplayPiecewiseProblem {
  id: string
  functionLetter: FunctionLetter
  pieces: PiecewisePiece[]
  evaluationPoint: number
  description: string // e.g., "Find f(2)"
  answer: PiecewiseAnswer
}

import type { PiecewisePiece } from '~/types/game'

export function evaluatePieceAtPoint(piece: PiecewisePiece, x: number): number {
  const { functionType, params } = piece
  const p = params || {}

  switch (functionType) {
    case 'constant':
      return numberOrZero(p.c)
    case 'linear':
      return numberOrZero(p.a) * x + numberOrZero(p.b)
    case 'quadratic':
      return numberOrZero(p.a) * x * x + numberOrZero(p.b) * x + numberOrZero(p.c)
    case 'absolute': {
      const h = numberOrZero(p.h)
      return numberOrZero(p.a) * Math.abs(x - h) + numberOrZero(p.c)
    }
    case 'square-root': {
      const h = numberOrZero(p.h)
      const rad = x - h
      if (rad < 0) return Number.NaN
      return numberOrZero(p.a) * Math.sqrt(rad) + numberOrZero(p.c)
    }
    default:
      return x
  }
}

function numberOrZero(n: unknown): number {
  const v = typeof n === 'number' ? n : 0
  return Number.isFinite(v) ? v : 0
}


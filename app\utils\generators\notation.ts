import type { DisplayNotationProblem, NotationAnswer } from '~/types/game'
import { randomInt } from './shared'

export function generateDisplayNotationProblem(): DisplayNotationProblem {
  const id = `display-notation-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const types: Array<'english' | 'set-builder' | 'interval'> = ['english', 'set-builder', 'interval']
  const type = types[randomInt(0, types.length - 1)]!

  const statementTypes = [
    'all-real',
    'greater-than',
    'greater-equal',
    'less-than',
    'less-equal',
    'between-open',
    'between-closed',
    'between-left-closed',
    'between-right-closed',
  ] as const

  const statementType = statementTypes[randomInt(0, statementTypes.length - 1)]!

  let content = ''
  let description = ''

  const a = randomInt(-10, 10)
  const b = randomInt(a + 1, a + 10)

  const answer: NotationAnswer = {
    english: generateEnglishStatement(statementType, a, b),
    setBuilder: generateSetBuilderNotation(statementType, a, b, 'x'),
    interval: generateIntervalNotation(statementType, a, b),
  }

  switch (type) {
    case 'english':
      description = 'English Statement'
      content = answer.english
      break
    case 'set-builder':
      description = 'Set-Builder Notation'
      content = answer.setBuilder
      break
    case 'interval':
      description = 'Interval Notation'
      content = answer.interval
      break
  }

  return { id, type, content, description, answer }
}

export function generateEnglishStatement(statementType: string, a: number, b: number): string {
  switch (statementType) {
    case 'all-real':
      return 'all real numbers'
    case 'greater-than':
      return `all numbers greater than ${a}`
    case 'greater-equal':
      return `all numbers greater than or equal to ${a}`
    case 'less-than':
      return `all numbers less than ${a}`
    case 'less-equal':
      return `all numbers less than or equal to ${a}`
    case 'between-open':
      return `all numbers between ${a} and ${b}`
    case 'between-closed':
      return `all numbers between ${a} and ${b}, including ${a} and ${b}`
    case 'between-left-closed':
      return `all numbers between ${a} and ${b}, including ${a} but not ${b}`
    case 'between-right-closed':
      return `all numbers between ${a} and ${b}, including ${b} but not ${a}`
    default:
      return 'all real numbers'
  }
}

export function generateSetBuilderNotation(
  statementType: string,
  a: number,
  b: number,
  variable: 'x' | 'y' = 'x',
): string {
  switch (statementType) {
    case 'all-real':
      return `{${variable} | ${variable} ∈ ℝ}`
    case 'greater-than':
      return `{${variable} | ${variable} > ${a}}`
    case 'greater-equal':
      return `{${variable} | ${variable} ≥ ${a}}`
    case 'less-than':
      return `{${variable} | ${variable} < ${a}}`
    case 'less-equal':
      return `{${variable} | ${variable} ≤ ${a}}`
    case 'between-open':
      return `{${variable} | ${a} < ${variable} < ${b}}`
    case 'between-closed':
      return `{${variable} | ${a} ≤ ${variable} ≤ ${b}}`
    case 'between-left-closed':
      return `{${variable} | ${a} ≤ ${variable} < ${b}}`
    case 'between-right-closed':
      return `{${variable} | ${a} < ${variable} ≤ ${b}}`
    default:
      return `{${variable} | ${variable} ∈ ℝ}`
  }
}

export function generateIntervalNotation(statementType: string, a: number, b: number): string {
  switch (statementType) {
    case 'all-real':
      return '(-∞, ∞)'
    case 'greater-than':
      return `(${a}, ∞)`
    case 'greater-equal':
      return `[${a}, ∞)`
    case 'less-than':
      return `(-∞, ${a})`
    case 'less-equal':
      return `(-∞, ${a}]`
    case 'between-open':
      return `(${a}, ${b})`
    case 'between-closed':
      return `[${a}, ${b}]`
    case 'between-left-closed':
      return `[${a}, ${b})`
    case 'between-right-closed':
      return `(${a}, ${b}]`
    default:
      return '(-∞, ∞)'
  }
}


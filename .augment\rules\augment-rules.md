---
type: "always_apply"
---

# Augment Development Rules

Essential coding rules optimized for AI-assisted development with Nuxt 4, Vue 3, TypeScript, and modern web practices.

## Core Principles

- Write code like a senior full-stack developer with expertise in: Nuxt 4, Vue 3, Nuxt UI v3+, Pinia, VueUse, Biome.js
- Always use pnpm as package manager
- Always use Nuxt UI v3.0+ with semantic colors and proper UApp wrapper
- Use descriptive names, keep functions small (<20 lines), follow single responsibility principle
- Leverage TypeScript's type system for code safety and maintainability
- Validate all inputs, sanitize outputs, follow security best practices
- Focus on elegant, maintainable solutions over verbose code
- Proactively address edge cases, race conditions, and security considerations
- Comment on 'why' not 'what' - assume code readability through well-named functions
- Consider performance implications and suggest optimization opportunities

## Nuxt 4 & Vue 3 Essentials

### Auto-Imports and File-Based Routing
**✅ DO:**
```typescript
// Use auto-imported composables - no manual imports needed
const route = useRoute()
const { data: user } = await useFetch(`/api/users/${route.params.id}`)
const theme = useState('theme', () => 'light')

// File-based routing: pages/users/[id].vue
<script setup lang="ts">
definePageMeta({ title: 'User Profile', layout: 'dashboard' })
</script>
```

**❌ DON'T:**
```typescript
// Don't manually import auto-imported composables
import { useRoute, useFetch } from '#app'
import { ref } from 'vue'
```

### Nuxt UI Integration
**✅ DO:**
```typescript
// Nuxt UI components with proper TypeScript
<template>
  <UCard>
    <template #header>
      <h3 class="text-base font-semibold">User Profile</h3>
    </template>

    <UForm :schema="schema" :state="state" @submit="onSubmit" @error="onError">
      <UFormField label="Name" name="name" required>
        <UInput v-model="state.name" />
      </UFormField>

      <UFormField label="Email" name="email" required>
        <UInput v-model="state.email" type="email" />
      </UFormField>

      <UButton type="submit" :loading="pending">
        Save Changes
      </UButton>
    </UForm>
  </UCard>
</template>

<script setup lang="ts">
import { z } from 'zod'

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email')
})

const state = reactive({
  name: '',
  email: ''
})

const pending = ref(false)

const onSubmit = async (data: any) => {
  pending.value = true
  try {
    await $fetch('/api/users', { method: 'POST', body: data })
  } finally {
    pending.value = false
  }
}

// Enhanced error handling
const onError = async (event: FormErrorEvent) => {
  if (event?.errors?.[0]?.id) {
    const element = document.getElementById(event.errors[0].id)
    element?.focus()
    element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}
</script>

// Dark mode with Nuxt UI
const colorMode = useColorMode()
const isDark = computed(() => colorMode.value === 'dark')

// Theme customization
<template>
  <UButton
    :color="isDark ? 'neutral' : 'primary'"
    variant="solid"
    @click="colorMode.preference = isDark ? 'light' : 'dark'"
  >
    Toggle Theme
  </UButton>
</template>
```

### Nuxt UI v3 Critical Updates
**✅ DO:**
```typescript
// Always wrap app with UApp (required for modals, toasts, overlays)
// app.vue
<template>
  <UApp>
    <NuxtPage />
  </UApp>
</template>

// Use v3 component names
<UFormField label="Email" name="email">  // v3: UFormField
  <UInput v-model="state.email" />
</UFormField>
<USeparator />  // v3: USeparator (was UDivider)
<UDropdownMenu :items="items" />  // v3: UDropdownMenu (was UDropdown)

// Use semantic colors instead of Tailwind colors
<UButton color="primary" />     // ✅ semantic
<UButton color="error" />       // ✅ semantic
<UAlert color="success" />      // ✅ semantic

// New overlay pattern with trigger-based approach
<UModal>
  <UButton>Open Modal</UButton>
  <template #content>
    <UCard>
      <h2>Modal Content</h2>
    </UCard>
  </template>
</UModal>

// Use onClick instead of click in item objects
const items = [{
  label: 'Edit',
  onClick: () => console.log('Edit clicked')  // v3: onClick
}]

// Both modal patterns are valid
<UModal v-model:open="isOpen" title="Modal">
  <template #body>
    <p>Content</p>
  </template>
</UModal>
```

**❌ DON'T:**
```typescript
// Don't install these separately - included in Nuxt UI v3
modules: [
  '@nuxt/ui',
  '@nuxt/fonts',  // ❌ Already included
  '@nuxt/icon',   // ❌ Already included
]

// Don't use v2 component names
<UFormGroup />   // ❌ Use UFormField
<UDivider />     // ❌ Use USeparator
<UDropdown />    // ❌ Use UDropdownMenu

// Don't use Tailwind colors directly
<UButton color="blue" />    // ❌ Use color="primary"
<UButton color="red" />     // ❌ Use color="error"
```

**Semantic Color System:**
- `primary` - Main brand actions
- `secondary` - Secondary brand actions
- `success` - Success states
- `info` - Informational states
- `warning` - Warning states
- `error` - Error/destructive actions
- `neutral` - Neutral/default styling

**Design Tokens for Consistent Theming:**
```typescript
// ✅ Use design tokens instead of manual dark mode classes
<p class="text-muted">Description</p>        // Instead of text-gray-500 dark:text-gray-400
<p class="text-highlighted">Title</p>        // Instead of text-gray-900 dark:text-white
<div class="bg-elevated">Card</div>          // Instead of bg-white dark:bg-gray-900
<div class="bg-default">Surface</div>        // Default background
<div class="border-default">Border</div>     // Default border color
```

### Form Validation Patterns
**✅ DO:**
```typescript
// Use Zod exclusively for schema validation
import * as z from 'zod'
import type { FormSubmitEvent, FormErrorEvent } from '@nuxt/ui'

// Define schemas using z.object() with validation rules
const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(8, 'Must be at least 8 characters')
})

// Use z.output for TypeScript type inference
type Schema = z.output<typeof schema>

const state = reactive<Partial<Schema>>({
  email: undefined,
  password: undefined
})

// Handle form submission with properly typed events
async function onSubmit(event: FormSubmitEvent<Schema>) {
  console.log(event.data)
}

// Implement error handling for accessibility
async function onError(event: FormErrorEvent) {
  if (event?.errors?.[0]?.id) {
    const element = document.getElementById(event.errors[0].id)
    element?.focus()
    element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// Custom form components with useFormField
const { inputId, emitFormBlur, emitFormInput, emitFormChange } = useFormField()

// Pass schema to UForm via :schema prop
<UForm :schema="schema" :state="state" @submit="onSubmit" @error="onError">
  <UFormField label="Email" name="email" required>
    <UInput v-model="state.email" />
  </UFormField>
  <UFormField label="Password" name="password" required>
    <UInput v-model="state.password" type="password" />
  </UFormField>
  <UButton type="submit">Submit</UButton>
</UForm>
```

### SSR-Safe Patterns
**✅ DO:**
```typescript
// Client-only components
<ClientOnly>
  <InteractiveChart />
  <template #fallback><div>Loading...</div></template>
</ClientOnly>

// SSR-safe state
const user = useState('user', () => null)
onMounted(() => {
  if (process.client) {
    // Browser-only code
  }
})
```

**❌ DON'T:**
```typescript
// Don't access browser APIs during SSR
const userAgent = navigator.userAgent // Error on server
const randomId = Math.random() // Hydration mismatch
```

### Composition API Patterns
**✅ DO:**
```typescript
<script setup lang="ts">
interface Props {
  userId: string
  onUpdate?: (user: User) => void
}

const props = defineProps<Props>()
const emit = defineEmits<{ update: [user: User] }>()

const user = ref<User | null>(null)
const loading = ref(false)

const fetchUser = async () => {
  loading.value = true
  try {
    const { data } = await $fetch<User>(`/api/users/${props.userId}`)
    user.value = data
  } finally {
    loading.value = false
  }
}

onMounted(fetchUser)

// Prefer ref for primitives, reactive for objects
const count = ref(0)  // ✅ for primitives
const user = reactive({ name: '', email: '' })  // ✅ for objects

// Dependency injection for deep component trees
// Parent component
provide('theme', readonly(theme))

// Child component (any level deep)
const theme = inject('theme', 'light')

// Teleport for modals/overlays
<Teleport to="body">
  <div class="modal-overlay">
    <div class="modal">{{ content }}</div>
  </div>
</Teleport>
</script>
```

### Performance Optimization
**✅ DO:**
```typescript
// Lazy loading
const LazyChart = defineAsyncComponent(() => import('~/components/Chart.vue'))

// Efficient data fetching
const { data: posts } = await useLazyFetch('/api/posts')

// Proper caching
const { data } = await useFetch('/api/data', {
  key: 'expensive-data',
  server: true,
  default: () => []
})
```

### Server Routes and Middleware
**✅ DO:**
```typescript
// Server API routes: server/api/users/[id].get.ts
export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')
  return await getUserById(id)
})

// Route middleware: middleware/auth.ts
export default defineNuxtRouteMiddleware((to) => {
  const { isAuthenticated } = useAuthStore()
  if (!isAuthenticated && to.path !== '/login') {
    return navigateTo('/login')
  }
})

// Layouts: layouts/dashboard.vue
<template>
  <div class="min-h-screen bg-gray-50">
    <nav><!-- Navigation --></nav>
    <main>
      <slot />
    </main>
  </div>
</template>
```

## Image Optimization

### NuxtImg and NuxtPicture Usage
**✅ DO:**
```typescript
// Responsive images with NuxtImg
<template>
  <NuxtImg
    src="/hero-image.jpg"
    alt="Hero banner"
    width="1200"
    height="600"
    sizes="sm:100vw md:50vw lg:400px"
    :placeholder="[50, 25, 75, 5]"
    loading="lazy"
  />

  <!-- Modern format optimization -->
  <NuxtPicture
    src="/product-image.jpg"
    alt="Product showcase"
    :img-attrs="{
      class: 'rounded-lg shadow-md',
      style: 'aspect-ratio: 16/9'
    }"
    sizes="sm:100vw md:50vw lg:400px"
    format="webp,avif,jpg"
  />

  <!-- Integration with Nuxt UI -->
  <UCard>
    <template #header>
      <NuxtImg
        src="/avatar.jpg"
        alt="User avatar"
        width="40"
        height="40"
        class="rounded-full"
        :placeholder="[20, 20, 75, 5]"
      />
    </template>
    <p>User content here</p>
  </UCard>
</template>

// Dynamic image optimization
<script setup lang="ts">
interface ImageProps {
  src: string
  alt: string
  width?: number
  height?: number
}

const optimizedImage = (props: ImageProps) => ({
  ...props,
  sizes: 'sm:100vw md:50vw lg:400px',
  format: 'webp,avif,jpg',
  quality: 80,
  loading: 'lazy' as const
})
</script>
```

**❌ DON'T:**
```typescript
// Don't use regular img tags for optimized images
<img src="/large-image.jpg" alt="Image" /> // No optimization

// Don't forget responsive sizing
<NuxtImg src="/image.jpg" width="1200" /> // Fixed width only
```

## SEO Optimization

### Meta Tags and Open Graph
**✅ DO:**
```typescript
// Dynamic SEO with useSeoMeta
<script setup lang="ts">
const route = useRoute()
const { data: post } = await useFetch(`/api/posts/${route.params.slug}`)

useSeoMeta({
  title: post.value?.title,
  description: post.value?.excerpt,
  ogTitle: post.value?.title,
  ogDescription: post.value?.excerpt,
  ogImage: post.value?.featuredImage,
  ogUrl: `https://example.com${route.path}`,
  twitterCard: 'summary_large_image',
  twitterImage: post.value?.featuredImage
})

// Structured data
useJsonld({
  '@context': 'https://schema.org',
  '@type': 'BlogPosting',
  headline: post.value?.title,
  description: post.value?.excerpt,
  author: {
    '@type': 'Person',
    name: post.value?.author.name
  },
  datePublished: post.value?.publishedAt,
  image: post.value?.featuredImage
})

// Dynamic head management
useHead({
  title: computed(() => `${post.value?.title} | My Blog`),
  link: [
    {
      rel: 'canonical',
      href: `https://example.com${route.path}`
    }
  ]
})
</script>

// Sitemap generation (nuxt.config.ts)
export default defineNuxtConfig({
  nitro: {
    prerender: {
      routes: ['/sitemap.xml']
    }
  }
})

// server/api/sitemap.xml.ts
export default defineEventHandler(async (event) => {
  const posts = await getPosts()

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      <url>
        <loc>https://example.com</loc>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
      </url>
      ${posts.map(post => `
        <url>
          <loc>https://example.com/blog/${post.slug}</loc>
          <lastmod>${post.updatedAt}</lastmod>
          <changefreq>weekly</changefreq>
          <priority>0.8</priority>
        </url>
      `).join('')}
    </urlset>`

  setHeader(event, 'content-type', 'application/xml')
  return sitemap
})
```

## Accessibility (a11y)

### ARIA and Semantic HTML
**✅ DO:**
```typescript
// Semantic HTML with Nuxt UI
<template>
  <main>
    <UCard>
      <template #header>
        <h1 class="text-2xl font-bold">Dashboard</h1>
      </template>

      <!-- Accessible form -->
      <UForm :schema="schema" :state="state" @submit="onSubmit" @error="onError">
        <UFormField
          label="Email Address"
          name="email"
          description="We'll never share your email"
          :error="errors.email"
        >
          <UInput
            v-model="state.email"
            type="email"
            :aria-describedby="errors.email ? 'email-error' : 'email-help'"
            :aria-invalid="!!errors.email"
          />
        </UFormField>

        <!-- Accessible button -->
        <UButton
          type="submit"
          :loading="pending"
          :aria-label="pending ? 'Saving changes...' : 'Save changes'"
        >
          Save
        </UButton>
      </UForm>

      <!-- Skip navigation -->
      <UButton
        variant="ghost"
        class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4"
        @click="skipToMain"
      >
        Skip to main content
      </UButton>

      <!-- Accessible modal -->
      <UModal
        v-model="isOpen"
        :ui="{ overlay: { background: 'bg-gray-200/75 dark:bg-gray-800/75' } }"
        role="dialog"
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <UCard>
          <template #header>
            <h2 id="modal-title">Confirm Action</h2>
          </template>
          <p id="modal-description">Are you sure you want to continue?</p>
        </UCard>
      </UModal>
    </UCard>
  </main>
</template>

<script setup lang="ts">
// Focus management
const skipToMain = () => {
  const main = document.querySelector('main')
  main?.focus()
}

// Keyboard navigation
const { escape } = useMagicKeys()
watch(escape, (pressed) => {
  if (pressed && isOpen.value) {
    isOpen.value = false
  }
})

// Screen reader announcements
const announce = (message: string) => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  document.body.appendChild(announcement)

  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}
</script>
```

**❌ DON'T:**
```typescript
// Don't use divs for interactive elements
<div @click="submit">Submit</div> // Use UButton instead

// Don't forget alt text
<NuxtImg src="/chart.png" /> // Missing alt attribute

// Don't rely only on color for information
<span class="text-red-500">Error</span> // Add icon or text
```

## TypeScript Core Patterns

### Type Safety
**✅ DO:**
```typescript
// Branded types for domain safety
type UserId = string & { readonly brand: unique symbol }
type Email = string & { readonly brand: unique symbol }
type ProblemId = string & { readonly brand: unique symbol }

// Proper interfaces
interface User {
  readonly id: UserId
  name: string
  email: Email
  role: 'admin' | 'user' | 'moderator'
  createdAt: Date
}

// Type guards
const isUser = (value: unknown): value is User => {
  return typeof value === 'object' && value !== null &&
         'id' in value && 'name' in value && 'email' in value
}

// Result types for error handling
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E }

// Safe API calls with Result pattern
const safeApiCall = async <T>(fn: () => Promise<T>): Promise<Result<T>> => {
  try {
    const data = await fn()
    return { success: true, data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error('Unknown error')
    }
  }
}

// Usage
const result = await safeApiCall(() => fetchUser(id))
if (result.success) {
  console.log(result.data.name)
} else {
  console.error(result.error.message)
}
```

**❌ DON'T:**
```typescript
// Don't use any types
const processData = (data: any): any => data.someProperty

// Don't ignore TypeScript errors
// @ts-ignore
user.nonExistentProperty = 'value'
```

### Utility Types
**✅ DO:**
```typescript
// Use utility types for transformations
type CreateUserInput = Omit<User, 'id' | 'createdAt'>
type UpdateUserInput = Partial<Pick<User, 'name' | 'email'>>

// Generic constraints
interface Repository<T> {
  findById(id: string): Promise<T | null>
  save(entity: T): Promise<T>
}
```

## Security Essentials

### Input Validation
**✅ DO:**
```typescript
// Comprehensive validation
const validateUserInput = (input: unknown): User => {
  if (typeof input !== 'object' || input === null) {
    throw new ValidationError('Input must be an object')
  }
  
  const data = input as Record<string, unknown>
  
  if (typeof data.email !== 'string' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    throw new ValidationError('Invalid email format')
  }
  
  return { ...data } as User
}

// File upload validation
const validateFile = (file: File): void => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
  const maxSize = 5 * 1024 * 1024 // 5MB
  
  if (!allowedTypes.includes(file.type)) {
    throw new ValidationError('Invalid file type')
  }
  if (file.size > maxSize) {
    throw new ValidationError('File too large')
  }
}
```

### XSS Prevention
**✅ DO:**
```typescript
// Safe element creation
const createSafeElement = (tag: string, content: string): HTMLElement => {
  const element = document.createElement(tag)
  element.textContent = content // Use textContent, not innerHTML
  return element
}

// CSRF protection
const apiCall = async (url: string, data: any) => {
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  
  return await $fetch(url, {
    method: 'POST',
    body: data,
    headers: {
      'X-CSRF-Token': csrfToken || ''
    }
  })
}
```

**❌ DON'T:**
```typescript
// Don't render user content directly
element.innerHTML = userComment // XSS vulnerability

// Don't expose secrets
const API_KEY = 'sk-1234567890abcdef' // Never hardcode secrets
```

## Performance & Testing Basics

### Memory Management
**✅ DO:**
```typescript
// Proper cleanup
onUnmounted(() => {
  clearInterval(intervalId)
  removeEventListener('scroll', handleScroll)
})

// Efficient data structures
const cache = new Map() // Use Map for dynamic keys
const weakCache = new WeakMap() // Auto garbage collection
```

### Async Patterns
**✅ DO:**
```typescript
// Parallel operations
const [user, posts, notifications] = await Promise.all([
  fetchUser(id),
  fetchPosts(id),
  fetchNotifications(id)
])

// Error handling with timeout
const withTimeout = <T>(promise: Promise<T>, ms: number): Promise<T> => {
  const timeout = new Promise<never>((_, reject) => 
    setTimeout(() => reject(new Error('Timeout')), ms)
  )
  return Promise.race([promise, timeout])
}
```

### Testing Fundamentals
**✅ DO:**
```typescript
// Descriptive test structure
describe('UserService', () => {
  it('should create user with valid data', async () => {
    // Arrange
    const userData = { name: 'John', email: '<EMAIL>' }
    
    // Act
    const result = await userService.createUser(userData)
    
    // Assert
    expect(result).toMatchObject({ name: 'John', email: '<EMAIL>' })
  })
})

// Mock external dependencies
const mockRepository = {
  save: jest.fn(),
  findById: jest.fn()
} as jest.Mocked<UserRepository>
```

## Common Anti-Patterns to Avoid

**❌ DON'T:**
```typescript
// Memory leaks
const globalCache = [] // Never cleaned up
setInterval(() => {}, 1000) // Never cleared

// Security vulnerabilities
const query = `SELECT * FROM users WHERE id = '${userId}'` // SQL injection
localStorage.setItem('authToken', token) // XSS accessible

// Performance issues
const items = reactive(largeArray) // Unnecessary reactivity
watch(largeObject, callback, { deep: true }) // Expensive deep watching

// Type safety violations
const user: any = await fetchUser() // Loses type safety
// @ts-ignore
user.invalidProperty = 'value' // Suppresses errors
```

## Environment & Configuration

### Secure Configuration
**✅ DO:**
```typescript
// Environment validation
const config = {
  apiUrl: process.env.API_URL || 'http://localhost:3000',
  dbUrl: process.env.DATABASE_URL,
  jwtSecret: process.env.JWT_SECRET
}

// Validate required env vars
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET']
const missing = requiredEnvVars.filter(key => !process.env[key])
if (missing.length > 0) {
  throw new Error(`Missing env vars: ${missing.join(', ')}`)
}

// Runtime config in Nuxt
export default defineNuxtConfig({
  runtimeConfig: {
    apiSecret: process.env.API_SECRET, // Private
    public: {
      apiBase: process.env.API_BASE // Public
    }
  }
})
```

**❌ DON'T:**
```typescript
// Don't hardcode secrets
const API_KEY = 'sk-1234567890abcdef'
const DB_PASSWORD = 'mypassword123'

// Don't expose secrets in public config
runtimeConfig: {
  public: {
    apiSecret: process.env.API_SECRET // Wrong - exposed to client
  }
}
```

### Error Handling Patterns
**✅ DO:**
```typescript
// Structured error handling
class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'AppError'
  }
}

// Result pattern for explicit error handling
const safeApiCall = async <T>(fn: () => Promise<T>): Promise<Result<T>> => {
  try {
    const data = await fn()
    return { success: true, data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error('Unknown error')
    }
  }
}

// Usage
const result = await safeApiCall(() => fetchUser(id))
if (result.success) {
  console.log(result.data.name)
} else {
  console.error(result.error.message)
}
```



## Pinia State Management

### Store Composition with TypeScript
**✅ DO:**
```typescript
// stores/auth.ts - Composition API store
export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)

  const login = async (credentials: LoginCredentials) => {
    const { data } = await $fetch<{ user: User, token: string }>('/api/auth/login', {
      method: 'POST',
      body: credentials
    })

    user.value = data.user
    const token = useCookie('auth-token', { httpOnly: true, secure: true })
    token.value = data.token
  }

  const logout = async () => {
    await $fetch('/api/auth/logout', { method: 'POST' })
    user.value = null
    useCookie('auth-token').value = null
  }

  return { user: readonly(user), isAuthenticated, login, logout }
})

// stores/products.ts - Feature store
export const useProductsStore = defineStore('products', () => {
  const products = ref<Product[]>([])
  const loading = ref(false)

  const featuredProducts = computed(() => products.value.filter(p => p.featured))

  const fetchProducts = async () => {
    loading.value = true
    try {
      const { data } = await $fetch<Product[]>('/api/products')
      products.value = data
    } finally {
      loading.value = false
    }
  }

  return { products: readonly(products), loading, featuredProducts, fetchProducts }
})

// Extract reactive refs while maintaining reactivity
const authStore = useAuthStore()
const { user, isAuthenticated } = storeToRefs(authStore)

// Reset store to initial state
const resetUserData = () => {
  authStore.$reset()
}

// Subscribe to store changes
authStore.$subscribe((mutation, state) => {
  console.log('Store changed:', mutation.type)
})
```

**❌ DON'T:**
```typescript
// Don't use Options API stores in Nuxt 4
export const useAuthStore = defineStore('auth', {
  state: () => ({ user: null }) // Use Composition API instead
})

// Don't mutate state directly outside actions
const authStore = useAuthStore()
authStore.user = newUser // Use authStore.login() instead
```

### SSR-Safe Patterns
**✅ DO:**
```typescript
// plugins/auth.client.ts - Client initialization
export default defineNuxtPlugin(async () => {
  const authStore = useAuthStore()
  if (process.client) {
    await authStore.fetchCurrentUser()
  }
})

// composables/useAuthGuard.ts - Route protection
export const useAuthGuard = () => {
  const authStore = useAuthStore()

  const requireAuth = () => {
    if (!authStore.isAuthenticated) {
      throw createError({ statusCode: 401, statusMessage: 'Authentication required' })
    }
  }

  return { requireAuth }
}
```

## VueUse Composables

### Core Patterns and SSR-Safe Usage
**✅ DO:**
```typescript
// Essential VueUse composables
<script setup lang="ts">
// Storage with SSR safety
const theme = useLocalStorage('theme', 'light')
const searchHistory = useSessionStorage<string[]>('search-history', [])

// State management
const [isDarkMode, toggleDarkMode] = useToggle(false)
const { count, inc, dec } = useCounter(0, { min: 0, max: 100 })

// Performance optimization
const searchQuery = ref('')
const debouncedQuery = useDebouncedRef(searchQuery, 300)

// Browser APIs (client-only)
const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)
const isOnline = useOnline()
const prefersDark = useMediaQuery('(prefers-color-scheme: dark)')

// DOM interactions
const target = ref<HTMLElement>()
const { isIntersecting } = useIntersectionObserver(target)

// Keyboard shortcuts
const { ctrl_s, escape } = useMagicKeys()
watch(ctrl_s, (pressed) => pressed && saveDocument())

// Async data management
const { state: userData, isLoading, execute } = useAsyncState(
  () => $fetch<User>(`/api/users/${route.params.id}`),
  null
)

// Auto-cleanup intervals
const { pause, resume } = useIntervalFn(() => {
  fetchLatestData()
}, 5000)

onUnmounted(() => pause())
</script>
```

**❌ DON'T:**
```typescript
// Don't use browser APIs without SSR checks
const { width } = useWindowSize() // Use with process.client check
const theme = useLocalStorage('theme') // Missing default value
```
```

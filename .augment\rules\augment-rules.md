---
type: "always_apply"
---

# Augment Development Rules

Essential coding rules optimized for AI-assisted development with Nuxt 4, Vue 3, TypeScript, and modern web practices.

## Core Principles

- Write code like a senior full-stack developer with expertise in: Nuxt 4, Vue 3, Nuxt UI v3+, Pinia, VueUse, Biome.js
- Always use pnpm as package manager
- Always use Nuxt UI v3.0+ with semantic colors and proper UApp wrapper
- Use descriptive names, keep functions small (<20 lines), follow single responsibility principle
- Leverage TypeScript's type system for code safety and maintainability
- Validate all inputs, sanitize outputs, follow security best practices
- Focus on elegant, maintainable solutions over verbose code
- Proactively address edge cases, race conditions, and security considerations
- Comment on 'why' not 'what' - assume code readability through well-named functions
- Consider performance implications and suggest optimization opportunities

## Nuxt 4 & Vue 3 Essentials

### Auto-Imports and File-Based Routing
**✅ DO:**
```typescript
// Use auto-imported composables - no manual imports needed
const route = useRoute()
const { data: user } = await useFetch(`/api/users/${route.params.id}`)
const theme = useState('theme', () => 'light')

// File-based routing: pages/users/[id].vue
<script setup lang="ts">
definePageMeta({ title: 'User Profile', layout: 'dashboard' })
</script>
```

**❌ DON'T:**
```typescript
// Don't manually import auto-imported composables
import { useRoute, useFetch } from '#app'
import { ref } from 'vue'
```

### Nuxt UI Integration
**✅ DO:**
```typescript
// Nuxt UI components with proper TypeScript
<template>
  <UCard>
    <template #header>
      <h3 class="text-base font-semibold">User Profile</h3>
    </template>

    <UForm :schema="schema" :state="state" @submit="onSubmit" @error="onError">
      <UFormField label="Name" name="name" required>
        <UInput v-model="state.name" />
      </UFormField>

      <UFormField label="Email" name="email" required>
        <UInput v-model="state.email" type="email" />
      </UFormField>

      <UButton type="submit" :loading="pending">
        Save Changes
      </UButton>
    </UForm>
  </UCard>
</template>

<script setup lang="ts">
import { z } from 'zod'

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email')
})

const state = reactive({
  name: '',
  email: ''
})

const pending = ref(false)

const onSubmit = async (data: any) => {
  pending.value = true
  try {
    await $fetch('/api/users', { method: 'POST', body: data })
  } finally {
    pending.value = false
  }
}

// Enhanced error handling
const onError = async (event: FormErrorEvent) => {
  if (event?.errors?.[0]?.id) {
    const element = document.getElementById(event.errors[0].id)
    element?.focus()
    element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}
</script>

// Dark mode with Nuxt UI
const colorMode = useColorMode()
const isDark = computed(() => colorMode.value === 'dark')

// Theme customization
<template>
  <UButton
    :color="isDark ? 'neutral' : 'primary'"
    variant="solid"
    @click="colorMode.preference = isDark ? 'light' : 'dark'"
  >
    Toggle Theme
  </UButton>
</template>
```

### Nuxt UI v3 Critical Updates
**✅ DO:**
```typescript
// Always wrap app with UApp (required for modals, toasts, overlays)
// app.vue
<template>
  <UApp>
    <NuxtPage />
  </UApp>
</template>

// Use v3 component names
<UFormField label="Email" name="email">  // v3: UFormField
  <UInput v-model="state.email" />
</UFormField>
<USeparator />  // v3: USeparator (was UDivider)
<UDropdownMenu :items="items" />  // v3: UDropdownMenu (was UDropdown)

// Use semantic colors instead of Tailwind colors
<UButton color="primary" />     // ✅ semantic
<UButton color="error" />       // ✅ semantic
<UAlert color="success" />      // ✅ semantic

// New overlay pattern with trigger-based approach
<UModal>
  <UButton>Open Modal</UButton>
  <template #content>
    <UCard>
      <h2>Modal Content</h2>
    </UCard>
  </template>
</UModal>

// Use onClick instead of click in item objects
const items = [{
  label: 'Edit',
  onClick: () => console.log('Edit clicked')  // v3: onClick
}]

// Both modal patterns are valid
<UModal v-model:open="isOpen" title="Modal">
  <template #body>
    <p>Content</p>
  </template>
</UModal>
```

**❌ DON'T:**
```typescript
// Don't install these separately - included in Nuxt UI v3
modules: [
  '@nuxt/ui',
  '@nuxt/fonts',  // ❌ Already included
  '@nuxt/icon',   // ❌ Already included
]

// Don't use v2 component names
<UFormGroup />   // ❌ Use UFormField
<UDivider />     // ❌ Use USeparator
<UDropdown />    // ❌ Use UDropdownMenu

// Don't use Tailwind colors directly
<UButton color="blue" />    // ❌ Use color="primary"
<UButton color="red" />     // ❌ Use color="error"
```

**Semantic Color System:**
- `primary` - Main brand actions
- `secondary` - Secondary brand actions
- `success` - Success states
- `info` - Informational states
- `warning` - Warning states
- `error` - Error/destructive actions
- `neutral` - Neutral/default styling

**Design Tokens for Consistent Theming:**
```typescript
// ✅ Use design tokens instead of manual dark mode classes
<p class="text-muted">Description</p>        // Instead of text-gray-500 dark:text-gray-400
<p class="text-highlighted">Title</p>        // Instead of text-gray-900 dark:text-white
<div class="bg-elevated">Card</div>          // Instead of bg-white dark:bg-gray-900
<div class="bg-default">Surface</div>        // Default background
<div class="border-default">Border</div>     // Default border color
```

### Form Validation Patterns
**✅ DO:**
```typescript
// Use Zod exclusively for schema validation
import * as z from 'zod'
import type { FormSubmitEvent, FormErrorEvent } from '@nuxt/ui'

// Define schemas using z.object() with validation rules
const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(8, 'Must be at least 8 characters')
})

// Use z.output for TypeScript type inference
type Schema = z.output<typeof schema>

const state = reactive<Partial<Schema>>({
  email: undefined,
  password: undefined
})

// Handle form submission with properly typed events
async function onSubmit(event: FormSubmitEvent<Schema>) {
  console.log(event.data)
}

// Implement error handling for accessibility
async function onError(event: FormErrorEvent) {
  if (event?.errors?.[0]?.id) {
    const element = document.getElementById(event.errors[0].id)
    element?.focus()
    element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// Custom form components with useFormField
const { inputId, emitFormBlur, emitFormInput, emitFormChange } = useFormField()

// Pass schema to UForm via :schema prop
<UForm :schema="schema" :state="state" @submit="onSubmit" @error="onError">
  <UFormField label="Email" name="email" required>
    <UInput v-model="state.email" />
  </UFormField>
  <UFormField label="Password" name="password" required>
    <UInput v-model="state.password" type="password" />
  </UFormField>
  <UButton type="submit">Submit</UButton>
</UForm>
```

### SSR-Safe Patterns
**✅ DO:**
```typescript
// Client-only components
<ClientOnly>
  <InteractiveChart />
  <template #fallback><div>Loading...</div></template>
</ClientOnly>

// SSR-safe state
const user = useState('user', () => null)
onMounted(() => {
  if (process.client) {
    // Browser-only code
  }
})
```

**❌ DON'T:**
```typescript
// Don't access browser APIs during SSR
const userAgent = navigator.userAgent // Error on server
const randomId = Math.random() // Hydration mismatch
```

### Plugins and Layouts
**✅ DO:**
```typescript
// plugins/my-plugin.ts
export default defineNuxtPlugin((nuxtApp) => {
  // Plugin logic here
  nuxtApp.provide('myPlugin', { message: 'Hello from plugin!' })
})

// Plugin with dependencies and order
export default defineNuxtPlugin({
  name: 'my-plugin',
  dependsOn: ['other-plugin'],
  setup(nuxtApp) {
    // Setup logic
  }
})

// Dynamic middleware registration in plugins
export default defineNuxtPlugin(() => {
  addRouteMiddleware('auth', (to) => {
    if (!isAuthenticated() && to.path !== '/login') {
      return navigateTo('/login')
    }
  })
})

// Layouts: layouts/dashboard.vue
<template>
  <div class="min-h-screen bg-gray-50">
    <nav><!-- Navigation --></nav>
    <main>
      <slot />
    </main>
  </div>
</template>

// Dynamic layout switching
const setLayout = () => {
  setPageLayout('admin')
}
```

### Page Meta and Validation
**✅ DO:**
```typescript
// Enhanced page meta with validation
definePageMeta({
  title: 'User Profile',
  layout: 'dashboard',
  middleware: ['auth', 'admin'],
  validate: async (route) => {
    return /^\d+$/.test(route.params.id)
  },
  key: route => route.params.id
})

// Route-specific rules
definePageMeta({
  layout: false,
  middleware: 'guest-only'
})
```

### Composition API Patterns
**✅ DO:**
```typescript
<script setup lang="ts">
interface Props {
  userId: string
  onUpdate?: (user: User) => void
}

const props = defineProps<Props>()
const emit = defineEmits<{ update: [user: User] }>()

const user = ref<User | null>(null)
const loading = ref(false)

const fetchUser = async () => {
  loading.value = true
  try {
    const { data } = await $fetch<User>(`/api/users/${props.userId}`)
    user.value = data
  } finally {
    loading.value = false
  }
}

onMounted(fetchUser)

// Prefer ref for primitives, reactive for objects
const count = ref(0)  // ✅ for primitives
const user = reactive({ name: '', email: '' })  // ✅ for objects

// Dependency injection for deep component trees
// Parent component
provide('theme', readonly(theme))

// Child component (any level deep)
const theme = inject('theme', 'light')

// Teleport for modals/overlays
<Teleport to="body">
  <div class="modal-overlay">
    <div class="modal">{{ content }}</div>
  </div>
</Teleport>
</script>
```

### Vue 3.4+ Modern Features
**✅ DO:**
```typescript
// defineModel for v-model (Vue 3.4+)
<script setup lang="ts">
const model = defineModel<string>()
const [modelValue, modifiers] = defineModel<string, 'trim' | 'uppercase'>()

// Handle modifiers
if (modifiers.trim) {
  model.value = model.value.trim()
}

// useTemplateRef for template refs (Vue 3.5+)
const inputRef = useTemplateRef<HTMLInputElement>('input')

onMounted(() => {
  inputRef.value?.focus()
})

// defineOptions for component options
defineOptions({
  name: 'MyComponent',
  inheritAttrs: false
})
</script>

<template>
  <input ref="input" v-model="model" />
</template>
```

### Built-in Components (Teleport & Suspense)
**✅ DO:**
```typescript
// Teleport for portal functionality
<template>
  <button @click="showModal = true">Open Modal</button>

  <Teleport to="body">
    <div v-if="showModal" class="modal-overlay">
      <div class="modal">
        <h2>Modal Title</h2>
        <p>Modal content</p>
        <button @click="showModal = false">Close</button>
      </div>
    </div>
  </Teleport>
</template>

// Suspense for async components
<template>
  <Suspense>
    <AsyncComponent />
    <template #fallback>
      <div class="loading">Loading...</div>
    </template>
  </Suspense>
</template>

// Async setup with top-level await
<script setup>
const data = await fetch('/api/data').then(r => r.json())
const user = await $fetch('/api/user')
</script>

// Nested Suspense with suspensible prop
<template>
  <Suspense>
    <OuterComponent>
      <Suspense suspensible>
        <InnerAsyncComponent />
      </Suspense>
    </OuterComponent>
  </Suspense>
</template>
```

### Performance Optimization
**✅ DO:**
```typescript
// Lazy loading
const LazyChart = defineAsyncComponent(() => import('~/components/Chart.vue'))

// Efficient data fetching
const { data: posts } = await useLazyFetch('/api/posts')

// Proper caching
const { data } = await useFetch('/api/data', {
  key: 'expensive-data',
  server: true,
  default: () => []
})

// v-memo for expensive lists
<div v-for="item in list" v-memo="[item.id, item.selected]">
  {{ item.name }}
</div>

// Computed stability (Vue 3.4+)
const computedObj = computed((oldValue) => {
  const newValue = { isEven: count.value % 2 === 0 }
  if (oldValue && oldValue.isEven === newValue.isEven) {
    return oldValue
  }
  return newValue
})

// Shallow reactivity for large data
const shallowArray = shallowRef([/* large list */])
// Correct way to update shallow refs
shallowArray.value = [...shallowArray.value, newItem]
```

### Server Routes and Middleware
**✅ DO:**
```typescript
// Dynamic route parameters with getRouterParam
// server/api/users/[id].get.ts
export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')
  return await getUserById(id)
})

// HTTP method-specific routes
// server/api/users.get.ts
export default defineEventHandler(() => {
  return { message: 'GET handler' }
})

// server/api/users.post.ts
export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  return await createUser(body)
})

// Runtime config access in server routes
export default defineEventHandler(async (event) => {
  const { apiSecret } = useRuntimeConfig(event)
  const result = await $fetch('https://api.example.com/data', {
    headers: { Authorization: `Bearer ${apiSecret}` }
  })
  return result
})

// Named middleware: middleware/auth.ts
export default defineNuxtRouteMiddleware((to) => {
  const { isAuthenticated } = useAuthStore()
  if (!isAuthenticated && to.path !== '/login') {
    return navigateTo('/login')
  }
})

// Global middleware: middleware/analytics.global.ts
export default defineNuxtRouteMiddleware((to, from) => {
  // Runs on every route change
  console.log('Navigating to:', to.path)
})

// Middleware with error handling
export default defineNuxtRouteMiddleware((to) => {
  if (to.params.id === 'forbidden') {
    throw createError({ statusCode: 404, statusMessage: 'Page Not Found' })
  }
})
```

## Image Optimization

### NuxtImg and NuxtPicture Usage
**✅ DO:**
```typescript
// Responsive images with NuxtImg
<template>
  <NuxtImg
    src="/hero-image.jpg"
    alt="Hero banner"
    width="1200"
    height="600"
    sizes="sm:100vw md:50vw lg:400px"
    :placeholder="[50, 25, 75, 5]"
    loading="lazy"
  />

  <!-- Modern format optimization -->
  <NuxtPicture
    src="/product-image.jpg"
    alt="Product showcase"
    :img-attrs="{
      class: 'rounded-lg shadow-md',
      style: 'aspect-ratio: 16/9'
    }"
    sizes="sm:100vw md:50vw lg:400px"
    format="webp,avif,jpg"
  />

  <!-- Integration with Nuxt UI -->
  <UCard>
    <template #header>
      <NuxtImg
        src="/avatar.jpg"
        alt="User avatar"
        width="40"
        height="40"
        class="rounded-full"
        :placeholder="[20, 20, 75, 5]"
      />
    </template>
    <p>User content here</p>
  </UCard>
</template>

// Dynamic image optimization
<script setup lang="ts">
interface ImageProps {
  src: string
  alt: string
  width?: number
  height?: number
}

const optimizedImage = (props: ImageProps) => ({
  ...props,
  sizes: 'sm:100vw md:50vw lg:400px',
  format: 'webp,avif,jpg',
  quality: 80,
  loading: 'lazy' as const
})
</script>
```

**❌ DON'T:**
```typescript
// Don't use regular img tags for optimized images
<img src="/large-image.jpg" alt="Image" /> // No optimization

// Don't forget responsive sizing
<NuxtImg src="/image.jpg" width="1200" /> // Fixed width only
```

## SEO Optimization

### Meta Tags and Open Graph
**✅ DO:**
```typescript
// Dynamic SEO with useSeoMeta
<script setup lang="ts">
const route = useRoute()
const { data: post } = await useFetch(`/api/posts/${route.params.slug}`)

useSeoMeta({
  title: post.value?.title,
  description: post.value?.excerpt,
  ogTitle: post.value?.title,
  ogDescription: post.value?.excerpt,
  ogImage: post.value?.featuredImage,
  ogUrl: `https://example.com${route.path}`,
  twitterCard: 'summary_large_image',
  twitterImage: post.value?.featuredImage
})

// Structured data
useJsonld({
  '@context': 'https://schema.org',
  '@type': 'BlogPosting',
  headline: post.value?.title,
  description: post.value?.excerpt,
  author: {
    '@type': 'Person',
    name: post.value?.author.name
  },
  datePublished: post.value?.publishedAt,
  image: post.value?.featuredImage
})

// Dynamic head management
useHead({
  title: computed(() => `${post.value?.title} | My Blog`),
  link: [
    {
      rel: 'canonical',
      href: `https://example.com${route.path}`
    }
  ]
})
</script>

// Sitemap generation (nuxt.config.ts)
export default defineNuxtConfig({
  nitro: {
    prerender: {
      routes: ['/sitemap.xml']
    }
  }
})

// server/api/sitemap.xml.ts
export default defineEventHandler(async (event) => {
  const posts = await getPosts()

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      <url>
        <loc>https://example.com</loc>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
      </url>
      ${posts.map(post => `
        <url>
          <loc>https://example.com/blog/${post.slug}</loc>
          <lastmod>${post.updatedAt}</lastmod>
          <changefreq>weekly</changefreq>
          <priority>0.8</priority>
        </url>
      `).join('')}
    </urlset>`

  setHeader(event, 'content-type', 'application/xml')
  return sitemap
})
```

## Accessibility (a11y)

### ARIA and Semantic HTML
**✅ DO:**
```typescript
// Semantic HTML with Nuxt UI
<template>
  <main>
    <UCard>
      <template #header>
        <h1 class="text-2xl font-bold">Dashboard</h1>
      </template>

      <!-- Accessible form -->
      <UForm :schema="schema" :state="state" @submit="onSubmit" @error="onError">
        <UFormField
          label="Email Address"
          name="email"
          description="We'll never share your email"
          :error="errors.email"
        >
          <UInput
            v-model="state.email"
            type="email"
            :aria-describedby="errors.email ? 'email-error' : 'email-help'"
            :aria-invalid="!!errors.email"
          />
        </UFormField>

        <!-- Accessible button -->
        <UButton
          type="submit"
          :loading="pending"
          :aria-label="pending ? 'Saving changes...' : 'Save changes'"
        >
          Save
        </UButton>
      </UForm>

      <!-- Skip navigation -->
      <UButton
        variant="ghost"
        class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4"
        @click="skipToMain"
      >
        Skip to main content
      </UButton>

      <!-- Accessible modal -->
      <UModal
        v-model="isOpen"
        :ui="{ overlay: { background: 'bg-gray-200/75 dark:bg-gray-800/75' } }"
        role="dialog"
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <UCard>
          <template #header>
            <h2 id="modal-title">Confirm Action</h2>
          </template>
          <p id="modal-description">Are you sure you want to continue?</p>
        </UCard>
      </UModal>
    </UCard>
  </main>
</template>

<script setup lang="ts">
// Focus management
const skipToMain = () => {
  const main = document.querySelector('main')
  main?.focus()
}

// Keyboard navigation
const { escape } = useMagicKeys()
watch(escape, (pressed) => {
  if (pressed && isOpen.value) {
    isOpen.value = false
  }
})

// Screen reader announcements
const announce = (message: string) => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  document.body.appendChild(announcement)

  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}
</script>
```

**❌ DON'T:**
```typescript
// Don't use divs for interactive elements
<div @click="submit">Submit</div> // Use UButton instead

// Don't forget alt text
<NuxtImg src="/chart.png" /> // Missing alt attribute

// Don't rely only on color for information
<span class="text-red-500">Error</span> // Add icon or text
```

## Advanced Reactivity Patterns

### Shallow Reactivity and Performance
**✅ DO:**
```typescript
// Shallow reactivity for performance optimization
const shallowState = shallowRef({
  /* large object */
})

// Correct way to update shallow refs
shallowState.value = { ...shallowState.value, newProp: 'value' }

// Custom refs for advanced use cases
const debouncedRef = customRef((track, trigger) => {
  let timeout
  return {
    get() {
      track()
      return value
    },
    set(newValue) {
      clearTimeout(timeout)
      timeout = setTimeout(() => {
        value = newValue
        trigger()
      }, delay)
    }
  }
})

// toRef for normalizing values (Vue 3.3+)
const propRef = toRef(() => props.foo) // readonly ref from getter
const stateRef = toRef(state, 'count') // synced ref from reactive property

// triggerRef for shallow ref updates
const shallow = shallowRef({ count: 0 })
shallow.value.count = 1 // Won't trigger
triggerRef(shallow) // Force trigger
```

## TypeScript Core Patterns

### Type Safety
**✅ DO:**
```typescript
// Branded types for domain safety
type UserId = string & { readonly brand: unique symbol }
type Email = string & { readonly brand: unique symbol }
type ProblemId = string & { readonly brand: unique symbol }

// Proper interfaces
interface User {
  readonly id: UserId
  name: string
  email: Email
  role: 'admin' | 'user' | 'moderator'
  createdAt: Date
}

// Type guards
const isUser = (value: unknown): value is User => {
  return typeof value === 'object' && value !== null &&
         'id' in value && 'name' in value && 'email' in value
}

// Template ref typing (Vue 3.5+)
const inputRef = useTemplateRef<HTMLInputElement>('input')

// Component instance typing
type ComponentType = InstanceType<typeof MyComponent>
const compRef = useTemplateRef<ComponentType>('comp')

// Injection key typing
const key = Symbol() as InjectionKey<string>
provide(key, 'value')
const injected = inject(key) // typed as string | undefined

// Result types for error handling
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E }

// Safe API calls with Result pattern
const safeApiCall = async <T>(fn: () => Promise<T>): Promise<Result<T>> => {
  try {
    const data = await fn()
    return { success: true, data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error('Unknown error')
    }
  }
}

// Usage
const result = await safeApiCall(() => fetchUser(id))
if (result.success) {
  console.log(result.data.name)
} else {
  console.error(result.error.message)
}
```

### Essential Discriminated Unions
**✅ DO:**
```typescript
// Basic discriminated unions for common patterns
type LoadingState =
  | { status: 'loading' }
  | { status: 'success'; data: any }
  | { status: 'error'; error: string }

// Type narrowing with discriminated unions
function handleState(state: LoadingState) {
  switch (state.status) {
    case 'loading':
      return 'Loading...'
    case 'success':
      return state.data
    case 'error':
      return `Error: ${state.error}`
  }
}

// API response patterns
type ApiResponse<T> =
  | { success: true; data: T }
  | { success: false; error: string }

// Form field states
type FieldState =
  | { type: 'text'; value: string }
  | { type: 'number'; value: number }
  | { type: 'boolean'; value: boolean }
```

### Essential Type Guards
**✅ DO:**
```typescript
// Basic type guards for common checks
function isString(value: unknown): value is string {
  return typeof value === 'string'
}

function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value)
}

function isDefined<T>(value: T | null | undefined): value is T {
  return value != null
}

// Array type guard
function isArrayOfStrings(value: unknown): value is string[] {
  return Array.isArray(value) && value.every(item => typeof item === 'string')
}

// Usage in components
function processUserInput(input: unknown) {
  if (isString(input)) {
    return input.trim()
  }
  if (isNumber(input)) {
    return input.toString()
  }
  throw new Error('Invalid input')
}
```

### Basic Assertion Functions
**✅ DO:**
```typescript
// Simple assertion functions for common cases
function assertDefined<T>(value: T | null | undefined): asserts value is T {
  if (value == null) {
    throw new Error('Value is null or undefined')
  }
}

function assertIsString(value: unknown): asserts value is string {
  if (typeof value !== 'string') {
    throw new Error('Expected string')
  }
}

// Usage in API handling
function processApiData(data: unknown) {
  assertDefined(data)
  assertIsString(data.id)

  // Now data.id is safely typed as string
  return data.id.toUpperCase()
}
```

**❌ DON'T:**
```typescript
// Don't use any types
const processData = (data: any): any => data.someProperty

// Don't ignore TypeScript errors
// @ts-ignore
user.nonExistentProperty = 'value'
```

### Utility Types
**✅ DO:**
```typescript
// Use utility types for transformations
type CreateUserInput = Omit<User, 'id' | 'createdAt'>
type UpdateUserInput = Partial<Pick<User, 'name' | 'email'>>

// Generic constraints
interface Repository<T> {
  findById(id: string): Promise<T | null>
  save(entity: T): Promise<T>
}
```

## Security Essentials

### Input Validation
**✅ DO:**
```typescript
// Comprehensive validation
const validateUserInput = (input: unknown): User => {
  if (typeof input !== 'object' || input === null) {
    throw new ValidationError('Input must be an object')
  }
  
  const data = input as Record<string, unknown>
  
  if (typeof data.email !== 'string' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    throw new ValidationError('Invalid email format')
  }
  
  return { ...data } as User
}

// File upload validation
const validateFile = (file: File): void => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
  const maxSize = 5 * 1024 * 1024 // 5MB
  
  if (!allowedTypes.includes(file.type)) {
    throw new ValidationError('Invalid file type')
  }
  if (file.size > maxSize) {
    throw new ValidationError('File too large')
  }
}
```

### XSS Prevention
**✅ DO:**
```typescript
// Safe element creation
const createSafeElement = (tag: string, content: string): HTMLElement => {
  const element = document.createElement(tag)
  element.textContent = content // Use textContent, not innerHTML
  return element
}

// CSRF protection
const apiCall = async (url: string, data: any) => {
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  
  return await $fetch(url, {
    method: 'POST',
    body: data,
    headers: {
      'X-CSRF-Token': csrfToken || ''
    }
  })
}
```

**❌ DON'T:**
```typescript
// Don't render user content directly
element.innerHTML = userComment // XSS vulnerability

// Don't expose secrets
const API_KEY = 'sk-1234567890abcdef' // Never hardcode secrets
```

## Performance & Testing Basics

### Memory Management
**✅ DO:**
```typescript
// Proper cleanup
onUnmounted(() => {
  clearInterval(intervalId)
  removeEventListener('scroll', handleScroll)
})

// Efficient data structures
const cache = new Map() // Use Map for dynamic keys
const weakCache = new WeakMap() // Auto garbage collection
```

### Async Patterns
**✅ DO:**
```typescript
// Parallel operations
const [user, posts, notifications] = await Promise.all([
  fetchUser(id),
  fetchPosts(id),
  fetchNotifications(id)
])

// Error handling with timeout
const withTimeout = <T>(promise: Promise<T>, ms: number): Promise<T> => {
  const timeout = new Promise<never>((_, reject) => 
    setTimeout(() => reject(new Error('Timeout')), ms)
  )
  return Promise.race([promise, timeout])
}
```

### Testing Fundamentals
**✅ DO:**
```typescript
// Descriptive test structure
describe('UserService', () => {
  it('should create user with valid data', async () => {
    // Arrange
    const userData = { name: 'John', email: '<EMAIL>' }
    
    // Act
    const result = await userService.createUser(userData)
    
    // Assert
    expect(result).toMatchObject({ name: 'John', email: '<EMAIL>' })
  })
})

// Mock external dependencies
const mockRepository = {
  save: jest.fn(),
  findById: jest.fn()
} as jest.Mocked<UserRepository>
```

## Common Anti-Patterns to Avoid

**❌ DON'T:**
```typescript
// Memory leaks
const globalCache = [] // Never cleaned up
setInterval(() => {}, 1000) // Never cleared

// Security vulnerabilities
const query = `SELECT * FROM users WHERE id = '${userId}'` // SQL injection
localStorage.setItem('authToken', token) // XSS accessible

// Performance issues
const items = reactive(largeArray) // Unnecessary reactivity
watch(largeObject, callback, { deep: true }) // Expensive deep watching

// Type safety violations
const user: any = await fetchUser() // Loses type safety
// @ts-ignore
user.invalidProperty = 'value' // Suppresses errors
```

## Environment & Configuration

### Secure Configuration
**✅ DO:**
```typescript
// Environment validation
const config = {
  apiUrl: process.env.API_URL || 'http://localhost:3000',
  dbUrl: process.env.DATABASE_URL,
  jwtSecret: process.env.JWT_SECRET
}

// Validate required env vars
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET']
const missing = requiredEnvVars.filter(key => !process.env[key])
if (missing.length > 0) {
  throw new Error(`Missing env vars: ${missing.join(', ')}`)
}

// Runtime config in Nuxt
export default defineNuxtConfig({
  runtimeConfig: {
    apiSecret: process.env.API_SECRET, // Private
    public: {
      apiBase: process.env.API_BASE // Public
    }
  }
})
```

**❌ DON'T:**
```typescript
// Don't hardcode secrets
const API_KEY = 'sk-1234567890abcdef'
const DB_PASSWORD = 'mypassword123'

// Don't expose secrets in public config
runtimeConfig: {
  public: {
    apiSecret: process.env.API_SECRET // Wrong - exposed to client
  }
}
```

## Error Handling

### Structured Error Classes
**✅ DO:**
```typescript
// Base error class with context
class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public context?: Record<string, any>
  ) {
    super(message)
    this.name = 'AppError'
  }
}

// Specific error types
class ValidationError extends AppError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', 400, { field })
  }
}

class NotFoundError extends AppError {
  constructor(resource: string, id: string) {
    super(`${resource} not found`, 'NOT_FOUND', 404, { resource, id })
  }
}

class UnauthorizedError extends AppError {
  constructor(message = 'Unauthorized') {
    super(message, 'UNAUTHORIZED', 401)
  }
}
```

### Result Pattern for Safe Operations
**✅ DO:**
```typescript
// Result type for explicit error handling
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E }

// Safe API calls
const safeApiCall = async <T>(fn: () => Promise<T>): Promise<Result<T>> => {
  try {
    const data = await fn()
    return { success: true, data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error('Unknown error')
    }
  }
}

// Safe synchronous operations
const safeParse = <T>(json: string): Result<T> => {
  try {
    const data = JSON.parse(json)
    return { success: true, data }
  } catch (error) {
    return { success: false, error: new Error('Invalid JSON') }
  }
}

// Usage patterns
const result = await safeApiCall(() => fetchUser(id))
if (result.success) {
  console.log(result.data.name)
} else {
  console.error(result.error.message)
  // Handle error appropriately
}
```

### Try-Catch Best Practices
**✅ DO:**
```typescript
// Specific error handling
async function updateUser(id: string, data: UserData) {
  try {
    const user = await userService.update(id, data)
    return user
  } catch (error) {
    if (error instanceof ValidationError) {
      throw new AppError('Invalid user data', 'VALIDATION_FAILED', 400)
    }
    if (error instanceof NotFoundError) {
      throw new AppError('User not found', 'USER_NOT_FOUND', 404)
    }
    // Re-throw unknown errors
    throw error
  }
}

// Resource cleanup with finally
async function processFile(filePath: string) {
  let fileHandle: FileHandle | null = null
  try {
    fileHandle = await fs.open(filePath, 'r')
    const data = await fileHandle.readFile()
    return processData(data)
  } catch (error) {
    console.error('File processing failed:', error)
    throw new AppError('File processing failed', 'FILE_ERROR', 500)
  } finally {
    await fileHandle?.close()
  }
}

// Error boundary pattern for components
const withErrorBoundary = <T extends any[]>(
  fn: (...args: T) => Promise<any>
) => {
  return async (...args: T) => {
    try {
      return await fn(...args)
    } catch (error) {
      console.error('Operation failed:', error)
      // Log to monitoring service
      // Show user-friendly message
      throw new AppError('Operation failed', 'OPERATION_ERROR', 500)
    }
  }
}
```

### User-Facing Error Display with Nuxt UI Toasts
**✅ DO:**
```typescript
// Toast error handling composable
export const useErrorHandler = () => {
  const toast = useToast()

  const handleError = (error: unknown, fallbackMessage = 'Something went wrong') => {
    let message = fallbackMessage
    let title = 'Error'

    if (error instanceof AppError) {
      message = error.message
      title = getErrorTitle(error.code)
    } else if (error instanceof Error) {
      message = error.message
    }

    toast.add({
      title,
      description: message,
      color: 'error',
      timeout: 5000,
      actions: [{
        label: 'Dismiss',
        click: () => {}
      }]
    })
  }

  const handleSuccess = (message: string, title = 'Success') => {
    toast.add({
      title,
      description: message,
      color: 'success',
      timeout: 3000
    })
  }

  return { handleError, handleSuccess }
}

// Error title mapping
function getErrorTitle(code: string): string {
  const titles: Record<string, string> = {
    'VALIDATION_ERROR': 'Validation Failed',
    'NOT_FOUND': 'Not Found',
    'UNAUTHORIZED': 'Access Denied',
    'NETWORK_ERROR': 'Connection Problem',
    'FILE_ERROR': 'File Operation Failed'
  }
  return titles[code] || 'Error'
}

// API error handling with toasts
export const useApiHandler = () => {
  const { handleError, handleSuccess } = useErrorHandler()

  const apiCall = async <T>(
    fn: () => Promise<T>,
    successMessage?: string
  ): Promise<T | null> => {
    try {
      const result = await fn()
      if (successMessage) {
        handleSuccess(successMessage)
      }
      return result
    } catch (error) {
      handleError(error)
      return null
    }
  }

  return { apiCall }
}

// Form submission with error handling
const { handleError, handleSuccess } = useErrorHandler()

const onSubmit = async (data: FormData) => {
  try {
    await $fetch('/api/users', { method: 'POST', body: data })
    handleSuccess('User created successfully!')
    await navigateTo('/users')
  } catch (error) {
    handleError(error, 'Failed to create user')
  }
}

// File upload with progress and error handling
const uploadFile = async (file: File) => {
  const { handleError, handleSuccess } = useErrorHandler()

  try {
    // Validate file first
    if (file.size > 5 * 1024 * 1024) {
      throw new ValidationError('File size must be less than 5MB')
    }

    const formData = new FormData()
    formData.append('file', file)

    await $fetch('/api/upload', {
      method: 'POST',
      body: formData
    })

    handleSuccess('File uploaded successfully!')
  } catch (error) {
    handleError(error, 'Failed to upload file')
  }
}

// Network error handling
const { $fetch } = useNuxtApp()

// Override $fetch to handle common errors
const apiClient = $fetch.create({
  onResponseError({ response }) {
    const { handleError } = useErrorHandler()

    switch (response.status) {
      case 401:
        handleError(new UnauthorizedError('Please log in to continue'))
        navigateTo('/login')
        break
      case 403:
        handleError(new AppError('You do not have permission to perform this action', 'FORBIDDEN', 403))
        break
      case 404:
        handleError(new NotFoundError('Resource', 'requested item'))
        break
      case 500:
        handleError(new AppError('Server error occurred', 'SERVER_ERROR', 500))
        break
      default:
        handleError(new AppError('An unexpected error occurred', 'UNKNOWN_ERROR', response.status))
    }
  }
})
```



## Pinia State Management

### Store Composition with TypeScript
**✅ DO:**
```typescript
// stores/auth.ts - Composition API store
export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)

  const login = async (credentials: LoginCredentials) => {
    const { data } = await $fetch<{ user: User, token: string }>('/api/auth/login', {
      method: 'POST',
      body: credentials
    })

    user.value = data.user
    const token = useCookie('auth-token', { httpOnly: true, secure: true })
    token.value = data.token
  }

  const logout = async () => {
    await $fetch('/api/auth/logout', { method: 'POST' })
    user.value = null
    useCookie('auth-token').value = null
  }

  // Custom $reset for setup stores
  function $reset() {
    user.value = null
  }

  return { user: readonly(user), isAuthenticated, login, logout, $reset }
})

// stores/products.ts - Feature store
export const useProductsStore = defineStore('products', () => {
  const products = ref<Product[]>([])
  const loading = ref(false)

  const featuredProducts = computed(() => products.value.filter(p => p.featured))

  const fetchProducts = async () => {
    loading.value = true
    try {
      const { data } = await $fetch<Product[]>('/api/products')
      products.value = data
    } finally {
      loading.value = false
    }
  }

  return { products: readonly(products), loading, featuredProducts, fetchProducts }
})

// Extract reactive refs while maintaining reactivity
const authStore = useAuthStore()
const { user, isAuthenticated } = storeToRefs(authStore)

// Reset store to initial state
const resetUserData = () => {
  authStore.$reset()
}

// Subscribe to store changes
authStore.$subscribe((mutation, state) => {
  console.log('Store changed:', mutation.type)
  localStorage.setItem('auth', JSON.stringify(state))
})
```

### Store Utilities and Methods
**✅ DO:**
```typescript
// storeToRefs for reactive destructuring
const authStore = useAuthStore()
const { user, isAuthenticated } = storeToRefs(authStore)
// Actions can be destructured directly
const { login, logout } = authStore

// $patch for state updates
store.$patch({ count: 24 })
store.$patch((state) => {
  state.items.push({ name: 'shoes', quantity: 1 })
})

// $subscribe for state change monitoring
authStore.$subscribe((mutation, state) => {
  // mutation.type: 'direct' | 'patch object' | 'patch function'
  // mutation.storeId: store identifier
  // mutation.payload: patch object (for 'patch object' type)
  localStorage.setItem('auth', JSON.stringify(state))
}, { flush: 'sync' }) // Optional: immediate execution

// $onAction for action monitoring
authStore.$onAction(({
  name, // action name
  store, // store instance
  args, // action arguments
  after, // hook after action returns or resolves
  onError, // hook if action throws or rejects
}) => {
  console.log(`Action "${name}" called with args:`, args)

  after((result) => {
    console.log(`Action "${name}" completed with result:`, result)
  })

  onError((error) => {
    console.error(`Action "${name}" failed:`, error)
  })
})
```

**❌ DON'T:**
```typescript
// Don't use Options API stores in Nuxt 4
export const useAuthStore = defineStore('auth', {
  state: () => ({ user: null }) // Use Composition API instead
})

// Don't mutate state directly outside actions
const authStore = useAuthStore()
authStore.user = newUser // Use authStore.login() instead

// Don't destructure state/getters without storeToRefs
const { user, isAuthenticated } = authStore // ❌ Loses reactivity
const { user, isAuthenticated } = storeToRefs(authStore) // ✅ Maintains reactivity
```

### SSR-Safe Patterns
**✅ DO:**
```typescript
// plugins/auth.client.ts - Client initialization
export default defineNuxtPlugin(async () => {
  const authStore = useAuthStore()
  if (process.client) {
    await authStore.fetchCurrentUser()
  }
})

// composables/useAuthGuard.ts - Route protection
export const useAuthGuard = () => {
  const authStore = useAuthStore()

  const requireAuth = () => {
    if (!authStore.isAuthenticated) {
      throw createError({ statusCode: 401, statusMessage: 'Authentication required' })
    }
  }

  return { requireAuth }
}
```

### SSR and Hydration Patterns
**✅ DO:**
```typescript
// SSR hydration with skipHydrate
import { defineStore, skipHydrate } from 'pinia'
import { useLocalStorage } from '@vueuse/core'

export const useAuthStore = defineStore('auth', () => {
  const token = useLocalStorage('auth-token', '')
  const preferences = useLocalStorage('user-preferences', {})

  return {
    token: skipHydrate(token), // Skip SSR hydration for client-only data
    preferences: skipHydrate(preferences),
  }
})

// Option store hydration
export const useSettingsStore = defineStore('settings', {
  state: () => ({
    theme: useLocalStorage('theme', 'light'),
  }),

  hydrate(state, initialState) {
    // Re-initialize composables on client
    state.theme = useLocalStorage('theme', 'light')
  },
})

// SSR state management
const pinia = createPinia()
const app = createApp(App)
app.use(pinia)

// Server-side: serialize state
if (import.meta.env.SSR) {
  // After rendering, state is available
  const serializedState = JSON.stringify(pinia.state.value)
}

// Client-side: hydrate state
if (process.client) {
  pinia.state.value = JSON.parse(window.__pinia)
}
```

### Testing Patterns
**✅ DO:**
```typescript
// Unit testing with setActivePinia
import { setActivePinia, createPinia } from 'pinia'
import { useCounterStore } from '../src/stores/counter'

describe('Counter Store', () => {
  beforeEach(() => {
    // Create a fresh pinia and make it active
    setActivePinia(createPinia())
  })

  it('increments', () => {
    const counter = useCounterStore()
    expect(counter.count).toBe(0)
    counter.increment()
    expect(counter.count).toBe(1)
  })

  it('increments by amount', () => {
    const counter = useCounterStore()
    counter.increment(10)
    expect(counter.count).toBe(10)
  })
})

// Component testing with createTestingPinia
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'

const wrapper = mount(Counter, {
  global: {
    plugins: [
      createTestingPinia({
        initialState: {
          counter: { count: 20 } // Start counter at 20 instead of 0
        },
        stubActions: false, // Execute actions instead of stubbing
        plugins: [somePlugin] // Include plugins
      })
    ]
  }
})

const store = useCounterStore() // Uses the testing pinia
store.count // 20

// Mock action return values with Vitest
import type { Mock } from 'vitest'

function mockedStore<TStoreDef extends () => unknown>(useStore: TStoreDef) {
  return useStore() as any
}

const store = mockedStore(useSomeStore)
store.someAction.mockResolvedValue('some value')
```

### Plugin Patterns
**✅ DO:**
```typescript
// Adding state with plugins
pinia.use(({ store }) => {
  if (!store.$state.hasOwnProperty('hasError')) {
    const hasError = ref(false)
    // Set on $state for SSR serialization
    store.$state.hasError = hasError
  }
  // Transfer ref from state to store for direct access
  store.hasError = toRef(store.$state, 'hasError')
})

// Custom $reset with plugins
pinia.use(({ store }) => {
  const originalReset = store.$reset.bind(store)

  return {
    $reset() {
      originalReset()
      store.hasError = false // Reset plugin-added state
    }
  }
})

// Subscribe to store changes in plugins
pinia.use(({ store }) => {
  store.$subscribe(() => {
    // React to store changes
  })
  store.$onAction(() => {
    // React to store actions
  })
})

// Custom options with plugins
defineStore('search', {
  actions: {
    searchContacts() {
      // ...
    }
  },
  debounce: {
    searchContacts: 300 // Custom option
  }
})

// Plugin reading custom options
pinia.use(({ options, store }) => {
  if (options.debounce) {
    return Object.keys(options.debounce).reduce((debouncedActions, action) => {
      debouncedActions[action] = debounce(
        store[action],
        options.debounce[action]
      )
      return debouncedActions
    }, {})
  }
})
```

### Advanced Store Composition
**✅ DO:**
```typescript
// Composable integration
export const useVideoPlayer = defineStore('video', () => {
  const videoElement = ref<HTMLVideoElement>()
  const src = ref('/data/video.mp4')
  const { playing, volume, currentTime } = useMediaControls(videoElement, { src })

  function loadVideo(element: HTMLVideoElement, videoSrc: string) {
    videoElement.value = element
    src.value = videoSrc
  }

  return {
    src,
    playing,
    volume,
    currentTime,
    loadVideo,
  }
})

// Store composition best practices
export const useCartStore = defineStore('cart', () => {
  // ✅ Call all useStore() at top level before any await
  const user = useUserStore()
  const auth = useAuthStore()

  const items = ref([])

  async function purchase() {
    try {
      // ✅ All store calls made before await
      await apiPurchase(user.id, items.value)
      // ❌ Don't call useStore() after await
    } catch (error) {
      // Handle error
    }
  }

  return { items, purchase }
})

// Prevent infinite loops in store composition
const useX = defineStore('x', () => {
  const y = useY()

  // ❌ This creates infinite loop
  // y.name

  function doSomething() {
    // ✅ Read properties in computed or actions
    const yName = y.name
  }

  return { name: ref('I am X') }
})
```

## VueUse Composables

### Core Patterns and SSR-Safe Usage
**✅ DO:**
```typescript
// Essential VueUse composables
<script setup lang="ts">
// Storage with SSR safety
const theme = useLocalStorage('theme', 'light')
const searchHistory = useSessionStorage<string[]>('search-history', [])

// State management
const [isDarkMode, toggleDarkMode] = useToggle(false)
const { count, inc, dec } = useCounter(0, { min: 0, max: 100 })

// Performance optimization
const searchQuery = ref('')
const debouncedQuery = useDebouncedRef(searchQuery, 300)

// Browser APIs (client-only)
const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)
const isOnline = useOnline()
const prefersDark = useMediaQuery('(prefers-color-scheme: dark)')

// DOM interactions
const target = ref<HTMLElement>()
const { isIntersecting } = useIntersectionObserver(target)

// Keyboard shortcuts
const { ctrl_s, escape } = useMagicKeys()
watch(ctrl_s, (pressed) => pressed && saveDocument())

// Async data management
const { state: userData, isLoading, execute } = useAsyncState(
  () => $fetch<User>(`/api/users/${route.params.id}`),
  null
)

// Auto-cleanup intervals
const { pause, resume } = useIntervalFn(() => {
  fetchLatestData()
}, 5000)

onUnmounted(() => pause())
</script>
```

**❌ DON'T:**
```typescript
// Don't use browser APIs without SSR checks
const { width } = useWindowSize() // Use with process.client check
const theme = useLocalStorage('theme') // Missing default value
```
```

// Minimal whitelist-based sanitizer for math inline HTML
// Allows only a small set of safe inline tags with no attributes

const ALLOWED_TAGS = new Set(['sup', 'sub', 'span'])

export function sanitizeInlineHtml(html: string): string {
  if (!html) return ''

  // Remove script/style/comment blocks entirely
  let out = html
    .replace(/<!--.*?-->/g, '')
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')

  // Strip all attributes from allowed tags and drop disallowed tags
  // 1) Temporarily replace allowed tags keeping content
  for (const tag of ALLOWED_TAGS) {
    // Opening tag with any attributes -> clean to <tag>
    const openRe = new RegExp(`<${tag}[^>]*>`, 'gi')
    out = out.replace(openRe, `<${tag}>`)
    // Closing tags preserved
  }

  // 2) Remove any other tags (keep their inner text)
  // Replace <anytag ...> with '' and </anytag> with '' for disallowed tags
  out = out.replace(/<([^\s>/]+)([^>]*)>/gi, (m, name) => {
    const n = String(name).toLowerCase()
    return ALLOWED_TAGS.has(n) ? m : ''
  })
  out = out.replace(/<\/([^>]+)>/gi, (m, name) => {
    const n = String(name).toLowerCase()
    return ALLOWED_TAGS.has(n) ? m : ''
  })

  return out
}


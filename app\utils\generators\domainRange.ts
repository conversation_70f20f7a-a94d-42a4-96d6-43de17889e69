import type { DisplayDomainRangeProblem, FunctionType } from '~/types/game'
import { formatFraction } from '~/utils/math/fraction'
import { formatCoeff, formatConstant, formatTerm } from '~/utils/formatting'
import { randomCoeff, randomInt } from './shared'

export function generateDisplayDomainRangeProblem(): DisplayDomainRangeProblem {
  const id = `display-domain-range-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const functionTypes: FunctionType[] = ['linear', 'quadratic', 'cubic', 'absolute', 'square-root']
  const functionType = functionTypes[randomInt(0, functionTypes.length - 1)]!

  const questionTypes: Array<'domain' | 'range'> = ['domain', 'range']
  const questionType = questionTypes[randomInt(0, questionTypes.length - 1)]!

  const functionData = generateFunctionWithDomainRange(functionType)

  return {
    id,
    functionType,
    equation: functionData.equation,
    questionType,
    description: `Find the ${questionType.charAt(0).toUpperCase() + questionType.slice(1)}`,
    answer: {
      domain: functionData.domain,
      range: functionData.range,
      domainSetBuilder: intervalToSetBuilder(functionData.domain, 'x'),
      rangeSetBuilder: intervalToSetBuilder(functionData.range, 'y'),
    },
  }
}

export function intervalToSetBuilder(interval: string, variable: 'x' | 'y' = 'x'): string {
  if (interval === '(-∞, ∞)') return `{${variable} | ${variable} ∈ ℝ}`

  const patterns = [
    { regex: /^\[(-?\d+(?:\/\d+)?), ∞\)$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${variable} ≥ ${m[1]}}` },
    { regex: /^\((-?\d+(?:\/\d+)?), ∞\)$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${variable} > ${m[1]}}` },
    { regex: /^\(-∞, (-?\d+(?:\/\d+)?)\]$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${variable} ≤ ${m[1]}}` },
    { regex: /^\(-∞, (-?\d+(?:\/\d+)?)\)$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${variable} < ${m[1]}}` },
    { regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\]$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${m[1]} ≤ ${variable} ≤ ${m[2]}}` },
    { regex: /^\((-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${m[1]} < ${variable} < ${m[2]}}` },
    { regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${m[1]} ≤ ${variable} ≤ ${m[2]}}` },
    { regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${m[1]} ≤ ${variable} ≤ ${m[2]}}` },
    { regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${m[1]} ≤ ${variable} ≤ ${m[2]}}` },
    { regex: /^\((-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\]$/,
      template: (m: RegExpMatchArray) => `{${variable} | ${m[1]} < ${variable} ≤ ${m[2]}}` },
  ] as const

  for (const p of patterns) {
    const match = interval.match(p.regex)
    if (match) return p.template(match)
  }
  return `{${variable} | ${variable} ∈ ℝ}`
}

function generateFunctionWithDomainRange(type: FunctionType): { equation: string; domain: string; range: string } {
  const a = randomCoeff()
  const b = randomInt(-5, 5)
  const c = randomInt(-5, 5)
  const h = randomInt(-3, 3)
  const k = randomInt(-3, 3)

  switch (type) {
    case 'linear':
      return { equation: `f(x) = ${formatCoeff(a)}x${formatConstant(b)}`, domain: '(-∞, ∞)', range: '(-∞, ∞)' }
    case 'quadratic': {
      const vertex_y_numerator = -b * b + 4 * a * c
      const vertex_y_denominator = 4 * a
      const vertex_y_fraction = formatFraction(vertex_y_numerator, vertex_y_denominator)
      return {
        equation: `f(x) = ${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstant(c)}`,
        domain: '(-∞, ∞)',
        range: a > 0 ? `[${vertex_y_fraction}, ∞)` : `(-∞, ${vertex_y_fraction}]`,
      }
    }
    case 'cubic': {
      const d = randomInt(-5, 5)
      return { equation: `f(x) = ${formatCoeff(a)}x³${formatTerm(b, 'x²')}${formatTerm(c, 'x')}${formatConstant(d)}`, domain: '(-∞, ∞)', range: '(-∞, ∞)' }
    }
    case 'absolute':
      return { equation: `f(x) = ${formatCoeff(a)}|x${formatConstant(-h, true)}|${formatConstant(k)}`, domain: '(-∞, ∞)', range: a > 0 ? `[${k}, ∞)` : `(-∞, ${k}]` }
    case 'square-root':
      return { equation: `f(x) = ${formatCoeff(a)}√(x${formatConstant(-h, true)})${formatConstant(k)}`, domain: `[${h}, ∞)`, range: a > 0 ? `[${k}, ∞)` : `(-∞, ${k}]` }
    default:
      return { equation: 'f(x) = x', domain: '(-∞, ∞)', range: '(-∞, ∞)' }
  }
}


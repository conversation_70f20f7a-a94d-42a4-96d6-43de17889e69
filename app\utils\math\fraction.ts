// Exact fraction formatting utilities
// Use exact arithmetic to avoid floating-point errors

export function formatFraction(numerator: number, denominator: number): string {
  // Handle special cases
  if (denominator === 0) return 'undefined'
  if (numerator === 0) return '0'

  // Handle negative fractions
  const isNegative = (numerator < 0) !== (denominator < 0)
  numerator = Math.abs(numerator)
  denominator = Math.abs(denominator)

  // Find GCD to simplify fraction
  const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b))
  const commonDivisor = gcd(numerator, denominator)

  numerator = numerator / commonDivisor
  denominator = denominator / commonDivisor

  // Format the result
  const sign = isNegative ? '-' : ''
  if (denominator === 1) return `${sign}${numerator}`
  return `${sign}${numerator}/${denominator}`
}


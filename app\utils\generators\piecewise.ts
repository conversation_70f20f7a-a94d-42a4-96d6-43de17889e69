import type {
  Display<PERSON><PERSON><PERSON>wiseProblem,
  FunctionLetter,
  FunctionType,
  PiecewisePiece,
} from '~/types/game'
import { evaluatePieceAtPoint } from '~/utils/evaluators/piecewiseEvaluator'
import { randomCoeff, randomInt, randomNonZeroInt } from './shared'
import { formatCoeff, formatNonZeroConstant, formatNonZeroTerm } from '~/utils/formatting'

export function generateDisplayPiecewiseProblem(): DisplayPiecewiseProblem {
  const id = `display-piecewise-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const functionLetters: FunctionLetter[] = ['f', 'g', 'h', 'p', 'q']
  const functionLetter = functionLetters[randomInt(0, functionLetters.length - 1)]!

  const numPieces = randomInt(3, 5)
  const pieces = generatePiecewisePieces(numPieces)
  const evaluationPoint = chooseEvaluationPoint(pieces)

  // Ensure nice radicand for the evaluated square-root piece
  postProcessSquareRootForEvaluation(pieces, evaluationPoint)

  const applicablePiece = findApplicablePiece(pieces, evaluationPoint)
  const result = evaluatePieceAtPoint(applicablePiece, evaluationPoint)

  const substitutionStep = `${functionLetter}(${evaluationPoint}) = ${formatSubstitutedEquation(applicablePiece.equation, evaluationPoint)}`
  const explanation = `Since ${evaluationPoint} satisfies the condition "${applicablePiece.condition}", we use the piece ${functionLetter}(x) = ${applicablePiece.equation}`

  return {
    id,
    functionLetter,
    pieces,
    evaluationPoint,
    description: `Find ${functionLetter}(${evaluationPoint})`,
    answer: {
      applicablePiece,
      substitutionStep,
      finalResult: result.toString(),
      explanation,
    },
  }
}

function generatePiecewisePieces(numPieces: number): PiecewisePiece[] {
  const pieces: PiecewisePiece[] = []

  const useSingleIntervals = randomInt(0, 1) === 1 && numPieces <= 3
  if (useSingleIntervals) return generateSingleIntervalPieces(numPieces)

  const domainStart = -6
  const domainEnd = 6
  const totalRange = domainEnd - domainStart
  const intervalSize = totalRange / numPieces

  for (let i = 0; i < numPieces; i++) {
    const leftBound = Math.round(domainStart + i * intervalSize)
    const rightBound =
      i === numPieces - 1 ? domainEnd : Math.round(domainStart + (i + 1) * intervalSize)

    let leftInclusive = i === 0 || randomInt(0, 1) === 1
    let rightInclusive = i === numPieces - 1 ? false : randomInt(0, 1) === 1

    if (i > 0) {
      const prev = pieces[i - 1]
      if (prev?.rightInclusive) leftInclusive = false
      else leftInclusive = true
    }

    const functionType = generateRandomFunctionType()
    const { equation, params } = generatePieceEquation(functionType)
    const condition = formatCondition(leftBound, rightBound, leftInclusive, rightInclusive)

    pieces.push({
      functionType,
      equation,
      condition,
      leftBound,
      rightBound,
      leftInclusive,
      rightInclusive,
      params,
    })
  }

  return pieces
}

function generateSingleIntervalPieces(numPieces: number): PiecewisePiece[] {
  const pieces: PiecewisePiece[] = []

  const boundaries: number[] = []
  for (let i = 0; i < numPieces - 1; i++) boundaries.push(randomInt(-5, 5))
  boundaries.sort((a, b) => a - b)

  for (let i = 0; i < numPieces; i++) {
    let condition: string
    let leftBound: number
    let rightBound: number
    let leftInclusive: boolean
    let rightInclusive: boolean

    if (i === 0) {
      rightBound = boundaries[0]!
      leftBound = -1000
      leftInclusive = false
      rightInclusive = randomInt(0, 1) === 1
      condition = `x ${rightInclusive ? '≤' : '<'} ${rightBound}`
    } else if (i === numPieces - 1) {
      leftBound = boundaries[i - 1]!
      rightBound = 1000
      const prev = pieces[i - 1]
      leftInclusive = prev?.rightInclusive ? false : true
      rightInclusive = false
      condition = `x ${leftInclusive ? '≥' : '>'} ${leftBound}`
    } else {
      leftBound = boundaries[i - 1]!
      rightBound = boundaries[i]!
      const prev = pieces[i - 1]
      leftInclusive = prev?.rightInclusive ? false : true
      rightInclusive = randomInt(0, 1) === 1
      const leftSymbol = leftInclusive ? '≤' : '<'
      const rightSymbol = rightInclusive ? '≤' : '<'
      condition = `${leftBound} ${leftSymbol} x ${rightSymbol} ${rightBound}`
    }

    const functionType = generateRandomFunctionType()
    const { equation, params } = generatePieceEquation(functionType)

    pieces.push({
      functionType,
      equation,
      condition,
      leftBound,
      rightBound,
      leftInclusive,
      rightInclusive,
      params,
    })
  }

  return pieces
}

function generateRandomFunctionType(): FunctionType {
  const types: FunctionType[] = ['linear', 'quadratic', 'constant', 'absolute', 'square-root']
  return types[randomInt(0, types.length - 1)]!
}

function generatePieceEquation(type: FunctionType): {
  equation: string
  params: PiecewisePiece['params']
} {
  const a = randomCoeff()
  const b = randomNonZeroInt(-5, 5)
  const c = randomNonZeroInt(-5, 5)
  let h = randomNonZeroInt(-3, 3)

  switch (type) {
    case 'constant': {
      const eq = `${c}`
      return { equation: eq, params: { c } }
    }
    case 'linear': {
      const eq = `${formatCoeff(a)}x${formatNonZeroConstant(b)}`
      return { equation: eq, params: { a, b } }
    }
    case 'quadratic': {
      const eq = `${formatCoeff(a)}x²${formatNonZeroTerm(b, 'x')}${formatNonZeroConstant(c)}`
      return { equation: eq, params: { a, b, c } }
    }
    case 'absolute': {
      const eq = `${formatCoeff(a)}|x${formatNonZeroConstant(-h)}|${formatNonZeroConstant(c)}`
      return { equation: eq, params: { a, h, c } }
    }
    case 'square-root': {
      const perfectSquares = [1, 4, 9, 16, 25]
      const targetSquare = perfectSquares[randomInt(0, perfectSquares.length - 1)]!
      h = -targetSquare
      const radicand = h === 0 ? 'x' : `x${formatNonZeroConstant(-h)}`
      const eq = `${formatCoeff(a)}√(${radicand})${formatNonZeroConstant(c)}`
      return { equation: eq, params: { a, h, c } }
    }
    default:
      return { equation: 'x', params: {} }
  }
}

function formatCondition(
  leftBound: number,
  rightBound: number,
  leftInclusive: boolean,
  rightInclusive: boolean,
): string {
  const leftSymbol = leftInclusive ? '≤' : '<'
  const rightSymbol = rightInclusive ? '≤' : '<'
  return `${leftBound} ${leftSymbol} x ${rightSymbol} ${rightBound}`
}

function chooseEvaluationPoint(pieces: PiecewisePiece[]): number {
  const piece = pieces[randomInt(0, pieces.length - 1)]!
  if (piece.leftBound === piece.rightBound) return piece.leftBound

  const effectiveLeft = Math.max(piece.leftBound, -10)
  const effectiveRight = Math.min(piece.rightBound, 10)
  const candidates: number[] = []

  for (let x = effectiveLeft; x <= effectiveRight; x++) {
    const leftOk = piece.leftInclusive ? x >= piece.leftBound : x > piece.leftBound
    const rightOk = piece.rightInclusive ? x <= piece.rightBound : x < piece.rightBound
    const radOk = piece.functionType === 'square-root' ? x - (piece.params?.h ?? 0) >= 0 : true
    if (leftOk && rightOk && radOk) candidates.push(x)
  }

  return candidates.length ? candidates[randomInt(0, candidates.length - 1)]! : effectiveLeft
}

function findApplicablePiece(pieces: PiecewisePiece[], x: number): PiecewisePiece {
  const found = pieces.find((piece) => {
    const leftOk = piece.leftInclusive ? x >= piece.leftBound : x > piece.leftBound
    const rightOk = piece.rightInclusive ? x <= piece.rightBound : x < piece.rightBound
    return leftOk && rightOk
  })
  if (!found) throw new Error(`No applicable piece found for x = ${x}`)
  return found
}

function postProcessSquareRootForEvaluation(pieces: PiecewisePiece[], x: number): void {
  for (const piece of pieces) {
    if (piece.functionType !== 'square-root') continue
    const leftOk = piece.leftInclusive ? x >= piece.leftBound : x > piece.leftBound
    const rightOk = piece.rightInclusive ? x <= piece.rightBound : x < piece.rightBound
    if (!(leftOk && rightOk)) continue

    // choose h so x - h is a perfect square
    const perfectSquares = [1, 4, 9, 16, 25, 36, 49]
    const target = perfectSquares[randomInt(0, perfectSquares.length - 1)]!
    const h = x - target
    const a = piece.params?.a ?? randomCoeff()
    const c = piece.params?.c ?? randomNonZeroInt(-5, 5)
    piece.params = { ...(piece.params || {}), a, c, h }
    const radicand = h === 0 ? 'x' : `x${formatNonZeroConstant(-h)}`
    const coefficient = formatCoeff(a)
    const constant = formatNonZeroConstant(c)
    piece.equation = `${coefficient}√(${radicand})${constant}`
    break
  }
}

function formatSubstitutedEquation(equation: string, value: number): string {
  return equation
    .replace(/x/g, `(${value})`)
    .replace(/√\(([^)]+)\)/g, (_, rad) => `√(${String(rad).replace(/\((\d+)\)/g, '$1')})`)
    .replace(/\(\((\d+)\)\)/g, '($1)')
    .replace(/\|([^|]+)\|/g, (_, c) => `|${String(c).replace(/\((\d+)\)/g, '$1')}|`)
}
